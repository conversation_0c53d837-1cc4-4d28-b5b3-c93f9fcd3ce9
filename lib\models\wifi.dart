import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../utils/user_context.dart';

class wifiConfig {
  final String homeSsid;
  final String homePassword;
  final String hotspotSsid;
  final String hotspotPassword;

  wifiConfig({
    required this.homeSsid,
    required this.homePassword,
    required this.hotspotSsid,
    required this.hotspotPassword,
  });

  factory wifiConfig.fromJson(Map<String, dynamic> json) {
    return wifiConfig(
      homeSsid: json['homeSsid'] as String,
      homePassword: json['homePassword'] as String,
      hotspotSsid: json['hotspotSsid'] as String,
      hotspotPassword: json['hotspotPassword'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
        'homeSsid': homeSsid,
        'homePassword': homePassword,
        'hotspotSsid': hotspotSsid,
        'hotspotPassword': hotspotPassword,
      };

  static Future<void> saveConfig(wifiConfig config) async {
    final storage = FlutterSecureStorage();
    final storageKey = UserContext.instance.getUserKey('wifiConfig');
    await storage.write(key: storageKey, value: jsonEncode(config.toJson()));
  }

  static Future<wifiConfig?> getConfig() async {
    final storage = FlutterSecureStorage();
    final storageKey = UserContext.instance.getUserKey('wifiConfig');
    final configJson = await storage.read(key: storageKey);
    if (configJson != null) {
      return wifiConfig.fromJson(jsonDecode(configJson));
    }
    return null;
  }
}
