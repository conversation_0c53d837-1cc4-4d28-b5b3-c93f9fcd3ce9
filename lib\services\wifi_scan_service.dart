import 'dart:async';
import 'package:wifi_scan/wifi_scan.dart';
import '../utils/app_logger.dart';

/// WiFi扫描服务
/// 提供扫描周围WiFi网络并按信号强度排序的功能
///
/// 使用示例：
/// ```dart
/// final wifiService = WifiScanService();
///
/// // 扫描WiFi网络
/// final accessPoints = await wifiService.scanWifiNetworks();
/// for (final ap in accessPoints) {
///   print('SSID: ${ap.ssid}, 信号强度: ${ap.level} dBm (${wifiService.getSignalStrengthDescription(ap.level)})');
/// }
///
/// // 持续扫描
/// final timer = wifiService.startContinuousWifiScan(
///   interval: Duration(seconds: 10),
///   onResults: (results) {
///     print('发现 ${results.length} 个WiFi网络');
///   },
/// );
///
/// // 停止持续扫描
/// timer?.cancel();
/// ```
class WifiScanService {
  /// 检查WiFi扫描是否可用
  Future<bool> isWifiScanSupported() async {
    try {
      final canGet = await WiFiScan.instance.canGetScannedResults();
      return canGet == CanGetScannedResults.yes;
    } catch (e, stackTrace) {
      AppLogger.error('检查WiFi扫描支持失败：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 检查位置权限是否已授权
  Future<bool> hasLocationPermission() async {
    try {
      final canStart = await WiFiScan.instance.canStartScan();
      return canStart == CanStartScan.yes;
    } catch (e, stackTrace) {
      AppLogger.error('检查位置权限失败：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 扫描周围的WiFi网络并按信号强度从大到小排列
  /// 返回排序后的WiFi网络列表
  Future<List<WiFiAccessPoint>> scanWifiNetworks() async {
    try {
      AppLogger.info('开始扫描WiFi网络');

      // 检查是否支持WiFi扫描
      final canScan = await hasLocationPermission();
      if (!canScan) {
        throw Exception('没有位置权限或WiFi扫描不可用');
      }

      // 开始扫描
      await WiFiScan.instance.startScan();

      // 等待扫描完成
      await Future.delayed(const Duration(seconds: 4));

      // 获取扫描结果
      final List<WiFiAccessPoint> accessPoints =
          await WiFiScan.instance.getScannedResults();

      // 按信号强度从大到小排序（信号强度值越大表示信号越强）
      accessPoints.sort((a, b) => b.level.compareTo(a.level));

      AppLogger.info('WiFi扫描完成，找到${accessPoints.length}个网络');

      return accessPoints;
    } catch (e, stackTrace) {
      AppLogger.error('WiFi扫描失败：$e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 持续扫描WiFi网络
  /// [interval] 扫描间隔时间，默认5秒
  /// [onResults] 扫描结果回调函数
  /// 返回可用于取消扫描的Timer
  Timer? startContinuousWifiScan({
    Duration interval = const Duration(seconds: 5),
    required Function(List<WiFiAccessPoint>) onResults,
  }) {
    try {
      AppLogger.info('开始持续WiFi扫描，间隔：${interval.inSeconds}秒');

      return Timer.periodic(interval, (timer) async {
        try {
          final results = await scanWifiNetworks();
          onResults(results);
        } catch (e) {
          AppLogger.error('持续扫描中发生错误：$e', error: e);
        }
      });
    } catch (e, stackTrace) {
      AppLogger.error('启动持续WiFi扫描失败：$e', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 根据SSID过滤WiFi网络
  /// [accessPoints] WiFi接入点列表
  /// [ssidPattern] SSID匹配模式（支持部分匹配）
  List<WiFiAccessPoint> filterBySsid(
      List<WiFiAccessPoint> accessPoints, String ssidPattern) {
    try {
      return accessPoints
          .where(
              (ap) => ap.ssid.toLowerCase().contains(ssidPattern.toLowerCase()))
          .toList();
    } catch (e, stackTrace) {
      AppLogger.error('按SSID过滤WiFi网络失败：$e', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  /// 根据信号强度过滤WiFi网络
  /// [accessPoints] WiFi接入点列表
  /// [minLevel] 最小信号强度（dBm）
  List<WiFiAccessPoint> filterBySignalStrength(
      List<WiFiAccessPoint> accessPoints, int minLevel) {
    try {
      return accessPoints.where((ap) => ap.level >= minLevel).toList();
    } catch (e, stackTrace) {
      AppLogger.error('按信号强度过滤WiFi网络失败：$e', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  /// 获取WiFi网络的详细信息字符串
  /// [accessPoint] WiFi接入点
  String getWifiDetails(WiFiAccessPoint accessPoint) {
    try {
      return '''
SSID: ${accessPoint.ssid}
BSSID: ${accessPoint.bssid}
信号强度: ${accessPoint.level} dBm
频率: ${accessPoint.frequency} MHz
能力: ${accessPoint.capabilities}
''';
    } catch (e, stackTrace) {
      AppLogger.error('获取WiFi详情失败：$e', error: e, stackTrace: stackTrace);
      return 'WiFi信息获取失败';
    }
  }

  /// 获取信号强度的文字描述
  /// [level] 信号强度值（dBm）
  String getSignalStrengthDescription(int level) {
    if (level >= -50) {
      return '极强';
    } else if (level >= -60) {
      return '很强';
    } else if (level >= -70) {
      return '强';
    } else if (level >= -80) {
      return '中等';
    } else if (level >= -90) {
      return '弱';
    } else {
      return '极弱';
    }
  }
}
