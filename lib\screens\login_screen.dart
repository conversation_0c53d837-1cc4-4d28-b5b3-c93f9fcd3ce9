import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/l10n.dart';
import '../constants/constants.dart';
import '../utils/app_logger.dart';
import '../utils/toast_utils.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  String _phone = '';
  String _smsCode = '';
  bool _isLoading = false;
  bool _isGettingCode = false;
  int _countDown = 60;
  Timer? _timer;
  bool? _agreedToTerms = false;

  TextEditingController _phoneController = TextEditingController();
  TextEditingController _smsCodeController = TextEditingController();

  void _login() async {
    if (_agreedToTerms != true) {
      ToastUtils.showError('请先同意用户协议和隐私政策');
      return;
    }

    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();

    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<AuthProvider>(context, listen: false)
          .login(_phone, _smsCode);
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(Routes.home);
      }
    } catch (e, stackTrace) {
      AppLogger.error('登录失败：$e', error: e, stackTrace: stackTrace);
      ToastUtils.showError(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 获取手机验证码
  void _getVerificationCode() async {
    if (_phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请先输入手机号')),
      );
      return;
    }

    if (!RegExp(r'^[0-9]{11}$').hasMatch(_phoneController.text)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请输入有效的手机号')),
      );
      return;
    }

    setState(() {
      _isGettingCode = true;
      _countDown = 60;
    });

    try {
      await Provider.of<AuthProvider>(context, listen: false)
          .getSmsCode(_phoneController.text);
      _timer = Timer.periodic(Duration(seconds: 1), (timer) {
        setState(() {
          if (_countDown > 0) {
            _countDown--;
          } else {
            _isGettingCode = false;
            _timer?.cancel();
          }
        });
      });
    } catch (e, stackTrace) {
      AppLogger.error('获取验证码失败：$e', error: e, stackTrace: stackTrace);
      ToastUtils.showError(e.toString());
      setState(() {
        _isGettingCode = false;
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    L10n.init(context); // 初始化本地化?

    return Scaffold(
      appBar: AppBar(
        // 2. 将AppBar的背景色设置为你想要的颜色
        backgroundColor: Colors.grey[60],
        elevation: 1,
        toolbarHeight: 0,
      ),
      backgroundColor: Colors.grey[60],
      resizeToAvoidBottomInset: true,
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, right: 16.0, top: 50.0),
              child: Column(
                children: [
                  const SizedBox(height: 10),

                  // Logo和标题部分
                  _buildHeader(),

                  const SizedBox(height: 35),

                  // 登录表单
                  _buildLoginForm(),

                  const SizedBox(height: 30),

                  // 用户协议部分
                  _buildAgreementSection(),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建头部Logo和标题
  Widget _buildHeader() {
    return Column(
      children: [
        // Logo图片 - 圆形剪裁
        Container(
          width: 140,
          height: 140,
          decoration: BoxDecoration(
            color: Colors.white70,
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.blue.shade500,
              width: 4,
            ),
          ),
          child: ClipOval(
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Image.asset(
                'assets/images/logo.png',
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.pets,
                    size: 60,
                    color: Colors.orange.shade400,
                  );
                },
              ),
            ),
          ),
        ),
        const SizedBox(height: 24),

        // 标题
        Text(
          '智心伴侣，知心朋友般的陪伴',
          style: TextStyle(
            fontSize: 20,
            color: Colors.grey.shade800,
          ),
        ),
      ],
    );
  }

  /// 构建登录表单
  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // 手机号输入框
          _buildPhoneField(),

          // 分隔线
          Divider(
            color: Colors.grey.shade300,
            height: 2,
            thickness: 0.5,
          ),

          // 验证码输入框
          _buildSmsCodeField(),

          // 分隔线
          Divider(
            color: Colors.grey.shade300,
            height: 2,
            thickness: 0.5,
          ),

          const SizedBox(height: 30),

          // 登录按钮
          SizedBox(
            width: double.infinity,
            height: 44,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _login,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.shade400,
                foregroundColor: Colors.white,
                elevation: 2,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      '登录',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建手机号输入框
  Widget _buildPhoneField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[60],
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _phoneController,
        keyboardType: TextInputType.phone,
        decoration: InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          hintText: '请输入手机号码',
          hintStyle: TextStyle(color: Colors.grey.shade500),
          prefixIcon: Icon(
            Icons.smartphone,
            color: Colors.black54,
            size: 23,
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) return '请输入手机号';
          if (!RegExp(r'^[0-9]{11}$').hasMatch(value)) return '请输入有效的手机号';
          return null;
        },
        onSaved: (value) => _phone = value!.trim(),
      ),
    );
  }

  /// 构建验证码输入框
  Widget _buildSmsCodeField() {
    return Container(
      margin: EdgeInsets.only(top: 6),
      decoration: BoxDecoration(
        color: Colors.grey[60],
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _smsCodeController,
        decoration: InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          hintText: '请输入验证码',
          hintStyle: TextStyle(color: Colors.grey.shade500),
          prefixIcon: Icon(
            Icons.lock_outline,
            color: Colors.black54,
            size: 23,
          ),
          suffixIcon: Container(
            margin: const EdgeInsets.all(4),
            child: TextButton(
              onPressed: _isGettingCode ? null : _getVerificationCode,
              style: TextButton.styleFrom(
                backgroundColor: _isGettingCode
                    ? Colors.grey.shade200
                    : Colors.green.shade50,
                foregroundColor: _isGettingCode
                    ? Colors.grey.shade500
                    : Colors.green.shade600,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              child: Text(
                _isGettingCode ? '${_countDown}s后重新获取' : '获取验证码',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) return '请输入验证码';
          if (value.length < 6) return '验证码长度至少为6位';
          return null;
        },
        onSaved: (value) => _smsCode = value!.trim(),
      ),
    );
  }

  /// 构建用户协议部分
  Widget _buildAgreementSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _agreedToTerms = !(_agreedToTerms ?? false);
            });
          },
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: (_agreedToTerms ?? false)
                  ? Colors.orange.shade400
                  : Colors.transparent,
              border: Border.all(
                color: (_agreedToTerms ?? false)
                    ? Colors.orange.shade400
                    : Colors.grey.shade400,
                width: 2,
              ),
              shape: BoxShape.circle, // 圆形复选框
            ),
            child: (_agreedToTerms ?? false)
                ? const Icon(
                    Icons.check,
                    size: 12,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text.rich(
            TextSpan(
              text: '同意',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              children: [
                TextSpan(
                  text: '《用户协议》',
                  style: TextStyle(
                    color: Colors.blue.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Navigator.of(context).pushNamed(Routes.userAgreement);
                    },
                ),
                const TextSpan(text: '、'),
                TextSpan(
                  text: '《隐私政策》',
                  style: TextStyle(
                    color: Colors.blue.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Navigator.of(context).pushNamed(Routes.privacyPolicy);
                    },
                ),
                const TextSpan(text: '并授权获取本机号码'),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
