// lib/utils/app_logger.dart
import 'package:logger/logger.dart'; // 核心日志库
import 'package:stack_trace/stack_trace.dart' as st; // 用于解析堆栈跟踪的辅助库

/// 自定义日志打印器，正确显示调用者位置而不是AppLogger内部位置
class _CustomLogPrinter extends LogPrinter {
  final PrettyPrinter _prettyPrinter;

  _CustomLogPrinter()
      : _prettyPrinter = PrettyPrinter(
          methodCount: 0, // 不显示默认的调用栈，我们自己处理
          errorMethodCount: 8, // 错误发生时显示的调用栈方法数量
          lineLength: 120, // 每行字符数，用于格式化输出
          colors: true, // 是否在控制台输出中使用颜色区分日志级别
          printEmojis: true, // 是否在日志级别前打印 Emoji 图标
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart, // 是否打印日志记录的时间戳
        );

  @override
  List<String> log(LogEvent event) {
    // 获取调用者信息
    final callerInfo = _getCallerInfo(event.stackTrace);

    // 创建新的LogEvent，在消息前添加调用者信息
    final modifiedEvent = LogEvent(
      event.level,
      '$callerInfo ${event.message}',
      time: event.time,
      error: event.error,
      stackTrace: event.stackTrace,
    );

    return _prettyPrinter.log(modifiedEvent);
  }

  /// 获取真实调用者信息（跳过AppLogger内部调用）
  String _getCallerInfo(StackTrace? stackTrace) {
    try {
      final trace = st.Trace.from(stackTrace ?? StackTrace.current);

      // 查找第一个不是AppLogger内部的调用
      for (int i = 0; i < trace.frames.length; i++) {
        final frame = trace.frames[i];
        final uri = frame.uri.toString();

        // 跳过AppLogger内部调用和Logger库内部调用
        if (!uri.contains('app_logger.dart') &&
            !uri.contains('package:logger/') &&
            !uri.contains('package:stack_trace/')) {
          // 从 URI 中提取文件名
          final path = frame.uri.pathSegments.isNotEmpty
              ? frame.uri.pathSegments.last
              : 'unknown_file';

          // 提取方法名，并清理
          String member = frame.member?.split('.').last ?? 'unknown_method';
          if (member.startsWith('<') && member.endsWith('>')) {
            member = 'anonymous_closure';
          }

          // 返回格式: "filename.dart (methodName:lineNumber:columnNumber)"
          return '($path $member:${frame.line}:${frame.column})';
        }
      }

      // 如果没有找到合适的调用者，返回默认值
      return '(unknown_caller)';
    } catch (e) {
      return '(caller_parse_error)';
    }
  }
}

class AppLogger {
  // 1. 私有静态 Logger 实例，使用自定义打印器
  static final Logger _logger = Logger(
    // 2. 配置自定义 Printer (格式化器)
    printer: _CustomLogPrinter(),
    // 3. 可选: 配置 LogFilter (过滤器)
    // filter: ProductionFilter(), // 例如，在生产环境中只输出警告及以上级别的日志

    // 4. 可选: 配置 LogOutput (输出目的地)
    // output: MultiOutput([ConsoleOutput(), FileOutput()]), // 例如，同时输出到控制台和文件
  );

  // 注意：调用者信息现在由自定义打印器 _CustomLogPrinter 自动处理

  // 6. 日志级别方法 (verbose, debug, info, warning, error, fatal)
  //    调用者信息由自定义打印器自动处理

  // info 方法
  static void info(String message, {String? moduleTag, Object? data}) {
    // 构建日志消息，moduleTag用于标识模块
    final logMessage = moduleTag != null
        ? "[$moduleTag] $message${data != null ? '\nData: $data' : ''}"
        : "$message${data != null ? '\nData: $data' : ''}";
    _logger.i(logMessage);
  }

  // error 方法
  static void error(
    String message, {
    String? moduleTag,
    required Object error, // 错误对象 (例如 Exception 实例)
    StackTrace? stackTrace, // 与错误关联的堆栈跟踪 (通常来自 try-catch)
  }) {
    // 构建日志消息
    final logMessage = moduleTag != null ? "[$moduleTag] $message" : message;

    _logger.e(
      logMessage,
      error: error,
      stackTrace: stackTrace ?? StackTrace.current,
    );
  }

  // verbose (最详细的日志)
  static void verbose(String message, {String? moduleTag, Object? data}) {
    final logMessage = moduleTag != null
        ? "[$moduleTag] $message${data != null ? '\nData: $data' : ''}"
        : "$message${data != null ? '\nData: $data' : ''}";
    _logger.t(logMessage); // 't' for trace/verbose
  }

  // debug
  static void debug(String message, {String? moduleTag, Object? data}) {
    final logMessage = moduleTag != null
        ? "[$moduleTag] $message${data != null ? '\nData: $data' : ''}"
        : "$message${data != null ? '\nData: $data' : ''}";
    _logger.d(logMessage);
  }

  // warning
  static void warning(String message,
      {String? moduleTag, Object? error, StackTrace? stackTrace}) {
    final logMessage = moduleTag != null ? "[$moduleTag] $message" : message;
    _logger.w(logMessage, error: error, stackTrace: stackTrace);
  }

  // fatal (比 error 更严重的错误)
  static void fatal(
    String message, {
    String? moduleTag,
    required Object error,
    StackTrace? stackTrace,
  }) {
    final logMessage = moduleTag != null ? "[$moduleTag] $message" : message;
    _logger.f(logMessage,
        error: error,
        stackTrace: stackTrace ?? StackTrace.current); // 'f' for fatal
  }
}
