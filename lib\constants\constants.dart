import 'package:flutter/material.dart';

// 全局 Navigator key
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class AppConfig {
  static const String apiBaseUrl = 'http://192.168.2.7:8000';
  static const String apiVersion = '/api';
  static const int requestTimeout = 15; // 秒
  static const String bleName = 'PETCARE';

  // 需要用户隔离的数据类型
  static const List<String> userDataKeys = [
    'petDevice',
    'healthData',
    'qrCode',
    'ultrasonicConfigs',
    'petInfo', // 爱宠信息
    'extended_health_data', // 扩展健康数据
  ];
}

/// 定义路由名称常量，方便管理和维护
class Routes {
  static const String home = '/home';
  static const String login = '/login';
  static const String profile = '/profile';

  static const String health = '/health';
  static const String petManagement = '/pet_management';
  static const String petInfoInput = '/pet_info_input';
  static const String petDetail = '/pet_detail';
  //超声波相关
  static const String ultrasonicConfig = '/ultrasonic_config_screen';
  //音频相关
  static const String audioRecord = '/audio_record_screen';
  //防丢二维码
  static const String qrCode = '/qr_screen';
  //设备管理
  static const String deviceScan = '/device_scan_screen';
  static const String deviceConnect = '/device_connected_screen';
  static const String deviceManage = '/device_manage_screen';
  static const String deviceControl = '/device_control_screen';
  static const String deviceRegistration = '/device_registration_screen';
  //用户协议和隐私政策
  static const String userAgreement = '/user_agreement';
  static const String privacyPolicy = '/privacy_policy';
  //开发过程产中测试使用
  static const String test = '/test';
}
