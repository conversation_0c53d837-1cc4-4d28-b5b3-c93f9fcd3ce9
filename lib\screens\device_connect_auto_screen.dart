import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/device_provider.dart';
import '../utils/app_logger.dart';
import '../models/wifi.dart';
import '../services/device_connect_auto_service.dart';

/// 自动配网管理组件
/// 作为后台服务，无UI界面，负责监听设备状态并自动进行配网
class DeviceConnectAutoScreen extends StatefulWidget {
  @override
  _DeviceConnectAutoScreenState createState() =>
      _DeviceConnectAutoScreenState();
}

class _DeviceConnectAutoScreenState extends State<DeviceConnectAutoScreen>
    with WidgetsBindingObserver {
  late DeviceConnectAutoService _autoConnectService;

  @override
  void initState() {
    super.initState();
    // 初始化自动配网服务
    _autoConnectService =
        Provider.of<DeviceConnectAutoService>(context, listen: false);
    // 设置回调函数
    _setupCallbacks();

    //测试
    _test();

    // 开始监听设备状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startDeviceMonitoring();
    });
  }

  @override
  void dispose() {
    _autoConnectService.stopMonitoring();
    _autoConnectService.dispose();
    super.dispose();
  }

  ///测试
  void _test() {
    wifiConfig wifi = wifiConfig(
      homeSsid: 'K50',
      homePassword: '12345678',
      hotspotSsid: 'note 10',
      hotspotPassword: '12345678',
    );
    wifiConfig.saveConfig(wifi);
  }

  /// 设置回调函数
  void _setupCallbacks() {
    // 配网完成回调
    _autoConnectService.setConfigCompleteCallback((success, message) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    });
  }

  /// 开始监听设备状态
  void _startDeviceMonitoring() {
    final deviceProvider = Provider.of<DeviceProvider>(context, listen: false);
    final device = deviceProvider.device;
    if (device != null && !device.isOnline) {
      _autoConnectService.startMonitoring(device);
      AppLogger.info('开始监听设备状态: ${device.deviceName}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceProvider>(
      builder: (context, deviceProvider, child) {
        _startDeviceMonitoring();
        // 返回空容器，因为这是后台服务组件
        return const SizedBox.shrink();
      },
    );
  }
}

/// 自动配网监控器Widget
/// 用于在应用的主要页面中集成自动配网功能
class AutoConnectMonitorWidget extends StatelessWidget {
  final Widget child;

  const AutoConnectMonitorWidget({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        // 添加自动配网组件作为后台服务
        DeviceConnectAutoScreen(),
      ],
    );
  }
}
