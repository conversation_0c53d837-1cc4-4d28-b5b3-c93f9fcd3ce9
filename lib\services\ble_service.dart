import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../utils/app_logger.dart';

class BleService {
  // 扫描设备
  Future<List<BluetoothDevice>> scanDevices(String bleName) async {
    List<BluetoothDevice> devices = [];
    try {
      // 监听扫描结果
      var subscription = FlutterBluePlus.scanResults.listen((results) {
        for (ScanResult r in results) {
          if (!devices.contains(r.device) && r.device.platformName == bleName) {
            devices.add(r.device);
          }
        }
      });
      // 开始扫描
      await FlutterBluePlus.startScan(timeout: Duration(seconds: 10));
      await FlutterBluePlus.stopScan();
      await subscription.cancel();

      return devices;
    } catch (e, stackTrace) {
      AppLogger.error('扫描设备失败：$e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  // 自动连接设备
  Future<void> connectToDevice(BluetoothDevice device, bool autoConnect) async {
    try {
      if (autoConnect) {
        await device.connect(autoConnect: true, mtu: null);
      } else {
        await device.connect(autoConnect: false);
      }
    } catch (e, stackTrace) {
      AppLogger.error('连接设备失败：$e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  //连接设备
  Future<void> disconnectDevice(BluetoothDevice device) async {
    try {
      await device.disconnect();
    } catch (e, stackTrace) {
      AppLogger.error('断开设备失败：$e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  //给定设备的蓝牙名称和MAC地址，快速扫描并连接蓝牙，返回连接结果。
  Future<bool> quickConnectAndDisconnect(
      String deviceName, String macAddress) async {
    try {
      BluetoothDevice? targetDevice;
      // var subscription = FlutterBluePlus.scanResults.listen((results) {
      //   for (ScanResult r in results) {
      //     if (r.device.remoteId.str == macAddress) {
      //       targetDevice = r.device;
      //       // 找到设备后立即停止扫描
      //       FlutterBluePlus.stopScan();
      //     }
      //   }
      // });

      // // 开始扫描
      // await FlutterBluePlus.startScan(
      //     withNames: [deviceName],
      //     withRemoteIds: [macAddress],
      //     timeout: Duration(seconds: 3));

      // // 等待一小段时间确保扫描结果被处理
      // await Future.delayed(Duration(seconds: 2));
      // await subscription.cancel();

      // if (targetDevice == null) {
      //   return false;
      // }

      //配mac地址
      targetDevice = BluetoothDevice.fromId(macAddress);

      // 连接设备
      await targetDevice.connect(autoConnect: false);

      // 等待1秒
      await Future.delayed(Duration(seconds: 4));

      // 断开连接
      await targetDevice.disconnect();

      return true;
    } catch (e, stackTrace) {
      AppLogger.error('蓝牙操作失败：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }
}
