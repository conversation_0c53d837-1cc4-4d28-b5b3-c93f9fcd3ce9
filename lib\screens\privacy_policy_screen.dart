import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

/// 隐私政策页面
class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  String _policyContent = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPolicyContent();
  }

  Future<void> _loadPolicyContent() async {
    try {
      final content =
          await rootBundle.loadString('assets/agreements/privacy_policy.md');
      setState(() {
        _policyContent = content;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _policyContent = '加载隐私政策内容失败，请稍后重试。';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '隐私政策',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.orange.shade400,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Column(
              children: [
                Expanded(
                  child: _buildMarkdownContent(),
                ),
              ],
            ),
    );
  }

  Widget _buildMarkdownContent() {
    return Markdown(
      data: _policyContent,
      selectable: true,
      padding: const EdgeInsets.all(20),
      styleSheet: MarkdownStyleSheet(
        p: TextStyle(
          fontSize: 15,
          height: 1.8,
          color: Colors.grey.shade800,
          letterSpacing: 0.3,
        ),
        h1: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade900,
          height: 1.5,
        ),
        h2: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade900,
          height: 1.5,
        ),
        h3: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade900,
          height: 1.5,
        ),
        h4: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade900,
          height: 1.5,
        ),
        h5: TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade900,
          height: 1.5,
        ),
        h6: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade900,
          height: 1.5,
        ),
        strong: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade900,
        ),
        em: TextStyle(
          fontStyle: FontStyle.italic,
          color: Colors.grey.shade800,
        ),
        listBullet: TextStyle(
          fontSize: 15,
          color: Colors.grey.shade800,
        ),
        blockquote: TextStyle(
          fontSize: 15,
          color: Colors.grey.shade700,
          fontStyle: FontStyle.italic,
        ),
        code: TextStyle(
          fontSize: 14,
          fontFamily: 'monospace',
          backgroundColor: Colors.grey.shade100,
          color: Colors.grey.shade800,
        ),
        codeblockDecoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(4),
        ),
        blockquoteDecoration: BoxDecoration(
          color: Colors.grey.shade50,
          border: Border(
            left: BorderSide(
              color: Colors.grey.shade400,
              width: 4,
            ),
          ),
        ),
      ),
    );
  }
}
