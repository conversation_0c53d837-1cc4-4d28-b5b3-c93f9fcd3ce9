import 'dart:async';
import 'package:flutter/material.dart';

import '../models/device.dart';
import '../utils/app_logger.dart';
import '../constants/constants.dart';
import 'device_connect_auto_service.dart';
import 'audio_service.dart';
import 'ble_service.dart';
import 'notification_service.dart';
import 'vibration_service.dart';

class AntiLostService {
  // 依赖注入的服务
  final BleService _bleService;
  final DeviceConnectAutoService _autoConnectService;

  // 构造函数接收依赖
  AntiLostService(this._bleService, this._autoConnectService);
  // 其它组件
  final AudioService _audioService = AudioService();
  final VibrationService _vibrationService = VibrationService();
  final NotificationService _notificationService = NotificationService();

  // 防丢功能扫描间隔时间，单位是分钟
  bool _isPrevent = false;
  // 计时器
  Timer? _reconnectTimer;

  // 标记是否已经在警报
  bool _isAlarming = false;

  // 警报ID
  static const int _notificationId = 101;

  // 标记是否从通知点击进入
  // bool _isFromNotificationTap = false;

  // 初始化服务
  Future<void> init() async {
    try {
      await _notificationService.init();

      // 注册通知点击回调
      // _notificationService.setOnNotificationTap(_handleNotificationTap);
    } catch (e, stackTrace) {
      AppLogger.error('初始化防丢服务失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // // 处理通知点击事件
  // void _handleNotificationTap(String? payload) {
  //   // 记录当前是从通知点击进入前台
  //   _isFromNotificationTap = true;
  // }

  // 检查设备连接
  Future<void> checkDeviceConnection(Device device,
      {VoidCallback? alarmCallback}) async {
    try {
      // 如果当前有配网流程正在进行，则不进行连接检查
      await Future.delayed(const Duration(seconds: 1));
      if (_autoConnectService.currentState != AutoConnectState.idle) {
        return;
      }

      if (_isAlarming || _isPrevent) {
        return;
      }

      bool isConnected = await _bleService.quickConnectAndDisconnect(
        device.deviceName,
        device.bleMac,
      );

      _isPrevent = true;
      while (isConnected) {
        await Future.delayed(const Duration(seconds: 3));
        isConnected = await _bleService.quickConnectAndDisconnect(
          device.deviceName,
          device.bleMac,
        );
      }
      _isPrevent = false;

      if (!isConnected && !_isAlarming) {
        // 设备连接失败，开始警报
        await startAlarm();
        // 执行回调函数
        alarmCallback?.call();
      }
    } catch (e, stackTrace) {
      AppLogger.error('检查设备连接失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 开始警报
  Future<void> startAlarm() async {
    try {
      if (_isAlarming) return; // 防止重复触发警报

      _isAlarming = true;

      // 播放音频警报（无论前后台都播放）
      await _audioService.playAlarm();

      // 触发振动（无论前后台都触发）
      await _vibrationService.vibrate();

      await _notificationService.showPetLostNotification(
        id: _notificationId,
        title: '宠物设备已离线',
        body: '您的宠物可能已经走丢，请立即查看！',
        payload: 'pet_lost',
      );
    } catch (e, stackTrace) {
      _isAlarming = false;
      AppLogger.error('启动警报失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 停止警报
  Future<void> stopAlarm() async {
    try {
      if (!_isAlarming) return;

      // 取消所有计时器
      _cancelTimers();

      // 停止音频警报
      await _audioService.stopAlarm();

      // 停止振动
      await _vibrationService.stopVibration();

      // 取消通知
      await _notificationService.cancelNotification(_notificationId);

      _isAlarming = false;
    } catch (e, stackTrace) {
      AppLogger.error('停止警报失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 用户知晓了警报，并将在10分钟后重新检查
  Future<void> acknowledgeAlarm(Device device) async {
    try {
      await stopAlarm();
      _isPrevent = true;
      // 10分钟后重新检查
      _reconnectTimer = Timer(const Duration(seconds: 1), () {
        _isPrevent = false;
      });
    } catch (e, stackTrace) {
      AppLogger.error('确认警报失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 设备在充电，一小时后重新检查
  Future<void> deviceCharging(Device device) async {
    try {
      await stopAlarm();
      _isPrevent = true;
      // 1小时后重新检查
      _reconnectTimer = Timer(const Duration(hours: 1), () {
        _isPrevent = false;
      });
    } catch (e, stackTrace) {
      AppLogger.error('设置设备充电模式失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 取消所有计时器
  void _cancelTimers() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  // 释放资源
  Future<void> dispose() async {
    try {
      _cancelTimers();
      await stopAlarm();
      await _audioService.dispose();
    } catch (e, stackTrace) {
      AppLogger.error('释放防丢服务资源失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 显示弹窗提示
  void showAlertDialog(Device device) {
    try {
      if (!_isAlarming) return;
      final globalContext = navigatorKey.currentContext;
      if (globalContext == null) return;
      showDialog(
        context: globalContext,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('宠物走失警报'),
            content: const Text('您的宠物设备已离线，可能已经走丢，请立即确认！'),
            actions: <Widget>[
              TextButton(
                child: const Text('我知道了'),
                onPressed: () {
                  acknowledgeAlarm(device);
                  Navigator.maybeOf(context)?.pop();
                },
              ),
              TextButton(
                child: const Text('短期内不再提醒'),
                onPressed: () {
                  deviceCharging(device);
                  Navigator.maybeOf(context)?.pop();
                },
              ),
            ],
          );
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('显示警报对话框失败：$e', error: e, stackTrace: stackTrace);
    }
  }
}
