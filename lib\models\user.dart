import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';

class User {
  final String phone;
  final String accessToken;
  final String refreshToken;
  final bool isLogin;

  User(
      {required this.phone,
      required this.accessToken,
      required this.refreshToken,
      required this.isLogin});

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      phone: json['phone'],
      accessToken: json['accessToken'],
      refreshToken: json['refreshToken'],
      isLogin: json['isLogin'],
    );
  }

  Map<String, dynamic> toJson() => {
        'phone': phone,
        'accessToken': accessToken,
        'refreshToken': refreshToken,
        'isLogin': isLogin,
      };

  static Future<User?> getUser() async {
    final storage = FlutterSecureStorage();

    // 首先尝试读取当前用户（兼容旧版本）
    final userJson = await storage.read(key: 'user');
    if (userJson != null) {
      final user = User.fromJson(jsonDecode(userJson));
      // 迁移到新的用户隔离存储
      await saveUser(user);
      await storage.delete(key: 'user');
      return user;
    }

    // 如果没有旧版本用户数据，尝试读取当前登录用户
    final currentUser = await getCurrentUser();
    return currentUser;
  }

  static Future<User?> getCurrentUser() async {
    final storage = FlutterSecureStorage();
    final currentUserPhone = await storage.read(key: 'current_user_phone');
    if (currentUserPhone != null) {
      return await getUserByPhone(currentUserPhone);
    }
    return null;
  }

  static Future<User?> getUserByPhone(String phone) async {
    final storage = FlutterSecureStorage();
    final userJson = await storage.read(key: 'user_$phone');
    if (userJson != null) {
      return User.fromJson(jsonDecode(userJson));
    }
    return null;
  }

  static Future<void> saveUser(User user) async {
    final storage = FlutterSecureStorage();
    // 保存用户数据
    await storage.write(
        key: 'user_${user.phone}', value: jsonEncode(user.toJson()));
    // 设置当前用户
    await storage.write(key: 'current_user_phone', value: user.phone);
  }

  static Future<void> clearUser() async {
    final storage = FlutterSecureStorage();

    // 获取当前用户手机号
    final currentUserPhone = await storage.read(key: 'current_user_phone');
    if (currentUserPhone != null) {
      // 删除当前用户数据
      await storage.delete(key: 'user_$currentUserPhone');
    }

    // 清除当前用户标记
    await storage.delete(key: 'current_user_phone');

    // 兼容旧版本：也删除旧的用户数据
    await storage.delete(key: 'user');
  }
}
